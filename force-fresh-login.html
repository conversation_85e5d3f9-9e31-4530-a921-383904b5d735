<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Fresh Login</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .status { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; max-height: 400px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>🔧 Force Fresh Login</h1>
    <p>This will completely clear all authentication data and force a fresh login.</p>
    
    <div>
        <button onclick="clearEverything()">🧹 Clear All Auth Data</button>
        <button onclick="checkSession()">🔍 Check Current Session</button>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Login Form:</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password">
        <button onclick="freshLogin()">🔑 Fresh Login</button>
    </div>
    
    <div id="status" class="status"></div>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0bW1ia2Nrb2hrdXN1dGJzYnN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzNjQzOTYsImV4cCI6MjA2NDk0MDM5Nn0._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        async function clearEverything() {
            try {
                log('🧹 Clearing all authentication data...');
                
                // Sign out from Supabase
                await supabase.auth.signOut();
                log('✅ Signed out from Supabase');
                
                // Clear all localStorage
                localStorage.clear();
                log('✅ Cleared localStorage');
                
                // Clear all sessionStorage
                sessionStorage.clear();
                log('✅ Cleared sessionStorage');
                
                // Clear all cookies
                document.cookie.split(";").forEach(function(c) { 
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                });
                log('✅ Cleared cookies');
                
                // Clear IndexedDB (where Supabase might store data)
                if ('indexedDB' in window) {
                    const databases = await indexedDB.databases();
                    for (const db of databases) {
                        if (db.name) {
                            indexedDB.deleteDatabase(db.name);
                            log('✅ Cleared IndexedDB: ' + db.name);
                        }
                    }
                }
                
                log('🎉 All authentication data cleared! You can now try a fresh login.');
                
            } catch (error) {
                log('❌ Error clearing data: ' + error.message);
            }
        }
        
        async function checkSession() {
            try {
                log('🔍 Checking current session...');
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    log('❌ Session error: ' + error.message);
                    return;
                }
                
                if (session) {
                    log('✅ Session found!');
                    log('📧 Email: ' + session.user.email);
                    log('🆔 Current User ID: ' + session.user.id);
                    log('🎯 Expected User ID: a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4');
                    log('✅ Match: ' + (session.user.id === 'a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4' ? 'YES' : 'NO'));
                } else {
                    log('❌ No active session');
                }
            } catch (error) {
                log('❌ Exception: ' + error.message);
            }
        }
        
        async function freshLogin() {
            try {
                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                
                if (!email || !password) {
                    log('❌ Please enter both email and password');
                    return;
                }
                
                log('🔑 Attempting fresh login...');
                log('📧 Email: ' + email);
                
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    log('❌ Login error: ' + error.message);
                    return;
                }
                
                if (data.session) {
                    log('✅ Login successful!');
                    log('🆔 New User ID: ' + data.session.user.id);
                    log('📧 Email: ' + data.session.user.email);
                    log('🎯 Expected User ID: a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4');
                    log('✅ Match: ' + (data.session.user.id === 'a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4' ? 'YES' : 'NO'));
                    
                    if (data.session.user.id === 'a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4') {
                        log('🎉 SUCCESS! User ID now matches the database. You can go back to your app.');
                    } else {
                        log('⚠️ User ID still doesn\'t match. There may be a deeper authentication issue.');
                    }
                } else {
                    log('❌ No session returned from login');
                }
                
            } catch (error) {
                log('❌ Login exception: ' + error.message);
            }
        }
        
        // Check session on page load
        window.onload = function() {
            log('🚀 Page loaded. Ready to clear authentication data.');
            checkSession();
        };
    </script>
</body>
</html>
