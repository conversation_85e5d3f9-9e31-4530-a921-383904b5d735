<!DOCTYPE html>
<html>
<head>
    <title>Test Login and Accounts</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Test Login and Accounts</h1>
    <div id="status"></div>
    
    <div style="margin: 20px 0;">
        <h3>Step 1: Login</h3>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>" style="margin-right: 10px;">
        <input type="password" id="password" placeholder="Password" value="password123" style="margin-right: 10px;">
        <button onclick="login()">Login</button>
        <button onclick="logout()">Logout</button>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Step 2: Test Accounts Query</h3>
        <button onclick="testAccounts()">Query Accounts</button>
        <button onclick="checkSession()">Check Session</button>
    </div>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                log('Attempting to login with: ' + email);
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    log('❌ Login error: ' + error.message);
                } else {
                    log('✅ Login successful!');
                    log('User ID: ' + data.user.id);
                    log('Email: ' + data.user.email);
                }
            } catch (error) {
                log('❌ Login exception: ' + error.message);
            }
        }
        
        async function logout() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) {
                    log('❌ Logout error: ' + error.message);
                } else {
                    log('✅ Logged out successfully');
                }
            } catch (error) {
                log('❌ Logout exception: ' + error.message);
            }
        }
        
        async function checkSession() {
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (error) {
                    log('❌ Session error: ' + error.message);
                } else if (session) {
                    log('✅ Active session found');
                    log('User ID: ' + session.user.id);
                    log('Email: ' + session.user.email);
                    log('Expected User ID: a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4');
                    log('User ID Match: ' + (session.user.id === 'a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4' ? 'YES' : 'NO'));
                } else {
                    log('❌ No active session');
                }
            } catch (error) {
                log('❌ Session check exception: ' + error.message);
            }
        }
        
        async function testAccounts() {
            try {
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    log('❌ Session error: ' + sessionError.message);
                    return;
                }
                
                if (!session) {
                    log('❌ No session - please login first');
                    return;
                }
                
                log('🔍 Querying accounts for user: ' + session.user.id);
                
                const { data, error } = await supabase
                    .from('linked_accounts')
                    .select('id, account_name, account_type, account_subtype, mask, is_active, created_at')
                    .eq('user_id', session.user.id)
                    .eq('is_active', true)
                    .order('created_at', { ascending: false });
                
                if (error) {
                    log('❌ Query error: ' + error.message);
                } else {
                    log('✅ Query successful!');
                    log('Found ' + data.length + ' accounts');
                    if (data.length > 0) {
                        data.forEach((account, index) => {
                            log(`Account ${index + 1}: ${account.account_name} (••••${account.mask})`);
                        });
                    } else {
                        log('No accounts found for this user');
                    }
                }
            } catch (error) {
                log('❌ Query exception: ' + error.message);
            }
        }
        
        // Auto-check session on load
        window.onload = function() {
            log('🚀 Page loaded - checking session...');
            checkSession();
        };
    </script>
    
    <style>
        #status {
            border: 1px solid #ccc;
            padding: 10px;
            height: 400px;
            overflow-y: scroll;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }
        
        input, button {
            padding: 8px;
            margin: 4px;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0056b3;
        }
    </style>
</body>
</html>
