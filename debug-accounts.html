<!DOCTYPE html>
<html>
<head>
    <title>Debug Accounts</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Debug Accounts</h1>
    <div id="status"></div>
    <button onclick="testAuth()">Test Auth</button>
    <button onclick="testAccounts()">Test Accounts Query</button>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            document.getElementById('status').innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        async function testAuth() {
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (error) {
                    log('Auth error: ' + error.message);
                    return;
                }
                
                if (session) {
                    log('User authenticated: ' + session.user.email);
                    log('User ID: ' + session.user.id);
                } else {
                    log('No active session');
                    // Try to sign in
                    const { data, error: signInError } = await supabase.auth.signInWithPassword({
                        email: '<EMAIL>',
                        password: 'password123'
                    });
                    
                    if (signInError) {
                        log('Sign in error: ' + signInError.message);
                    } else {
                        log('Signed in successfully: ' + data.user.email);
                    }
                }
            } catch (error) {
                log('Error: ' + error.message);
            }
        }
        
        async function testAccounts() {
            try {
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    log('Session error: ' + sessionError.message);
                    return;
                }
                
                if (!session) {
                    log('No session - please authenticate first');
                    return;
                }
                
                log('Querying accounts for user: ' + session.user.id);
                
                const { data, error } = await supabase
                    .from('linked_accounts')
                    .select('id, account_name, account_type, account_subtype, mask, is_active, created_at')
                    .eq('user_id', session.user.id)
                    .eq('is_active', true)
                    .order('created_at', { ascending: false });
                
                if (error) {
                    log('Query error: ' + error.message);
                } else {
                    log('Found ' + data.length + ' accounts');
                    log('Accounts: ' + JSON.stringify(data, null, 2));
                }
            } catch (error) {
                log('Error: ' + error.message);
            }
        }
        
        // Auto-test on load
        window.onload = function() {
            testAuth();
        };
    </script>
</body>
</html>
