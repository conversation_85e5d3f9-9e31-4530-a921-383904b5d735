<!DOCTYPE html>
<html>
<head>
    <title>Debug Accounts</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Debug Accounts</h1>
    <div id="status"></div>
    <button onclick="testAuth()">Test Auth</button>
    <button onclick="testAccounts()">Test Accounts Query</button>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            document.getElementById('status').innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        async function testAuth() {
            try {
                const { data: { session }, error } = await supabase.auth.getSession();
                if (error) {
                    log('Auth error: ' + error.message);
                    return;
                }
                
                if (session) {
                    log('User authenticated: ' + session.user.email);
                    log('User ID: ' + session.user.id);
                } else {
                    log('No active session');
                    // Try to sign in
                    const { data, error: signInError } = await supabase.auth.signInWithPassword({
                        email: '<EMAIL>',
                        password: 'password123'
                    });
                    
                    if (signInError) {
                        log('Sign in error: ' + signInError.message);
                    } else {
                        log('Signed in successfully: ' + data.user.email);
                    }
                }
            } catch (error) {
                log('Error: ' + error.message);
            }
        }
        
        async function testAccounts() {
            try {
                const { data: { session }, error: sessionError } = await supabase.auth.getSession();
                if (sessionError) {
                    log('Session error: ' + sessionError.message);
                    return;
                }

                if (!session) {
                    log('No session - please authenticate first');
                    return;
                }

                log('Session details:');
                log('- User ID: ' + session.user.id);
                log('- Email: ' + session.user.email);
                log('- Access token present: ' + (session.access_token ? 'Yes' : 'No'));
                log('- Expires at: ' + new Date(session.expires_at * 1000).toLocaleString());

                log('Querying accounts for user: ' + session.user.id);

                // First, let's try to query all accounts (for debugging)
                const { data: allAccounts, error: allError } = await supabase
                    .from('linked_accounts')
                    .select('id, user_id, account_name, account_type, is_active')
                    .limit(5);

                if (allError) {
                    log('Error querying all accounts: ' + allError.message);
                } else {
                    log('All accounts query result: ' + JSON.stringify(allAccounts, null, 2));
                }

                // Now query for this specific user
                const { data, error } = await supabase
                    .from('linked_accounts')
                    .select('id, account_name, account_type, account_subtype, mask, is_active, created_at, user_id')
                    .eq('user_id', session.user.id)
                    .eq('is_active', true)
                    .order('created_at', { ascending: false });

                if (error) {
                    log('Query error: ' + error.message);
                    log('Error details: ' + JSON.stringify(error, null, 2));
                } else {
                    log('Found ' + data.length + ' accounts for user ' + session.user.id);
                    log('Accounts: ' + JSON.stringify(data, null, 2));
                }
            } catch (error) {
                log('Error: ' + error.message);
            }
        }
        
        // Auto-test on load
        window.onload = function() {
            testAuth();
        };
    </script>
</body>
</html>
