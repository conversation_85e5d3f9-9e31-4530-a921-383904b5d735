
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Trash2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

interface LinkedAccount {
  id: string;
  account_name: string;
  account_type: string;
  account_subtype: string;
  mask: string;
  is_active: boolean;
  created_at: string;
}

interface LinkedAccountsProps {
  refreshKey?: number;
}

export const LinkedAccounts = ({ refreshKey }: LinkedAccountsProps) => {
  const [accounts, setAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();

  const fetchLinkedAccounts = async () => {
    if (!user) {
      console.log('LinkedAccounts: No user found, skipping fetch');
      setLoading(false);
      return;
    }

    console.log('LinkedAccounts: Fetching accounts for user:', user.id);

    try {
      // First, let's check the current session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      console.log('LinkedAccounts: Current session:', { session: !!session, sessionError, userId: session?.user?.id });

      const { data, error } = await supabase
        .from('linked_accounts')
        .select('id, account_name, account_type, account_subtype, mask, is_active, created_at, user_id')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      console.log('LinkedAccounts: Query result:', { data, error, userIdQueried: user.id });

      if (error) {
        console.error('LinkedAccounts: Database error:', error);
        throw error;
      }

      console.log('LinkedAccounts: Setting accounts:', data || []);

      // Debug: Let's also try to query all accounts to see if there's a permission issue
      try {
        const { data: allAccounts, error: allError } = await supabase
          .from('linked_accounts')
          .select('id, user_id, account_name, is_active')
          .limit(5);
        console.log('LinkedAccounts: All accounts query (debug):', { allAccounts, allError });
      } catch (debugError) {
        console.log('LinkedAccounts: Debug query failed:', debugError);
      }

      setAccounts(data || []);
    } catch (error) {
      console.error('Error fetching linked accounts:', error);
      toast({
        title: "Error loading accounts",
        description: `Failed to load your linked accounts: ${error.message}`,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveAccount = async (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('linked_accounts')
        .update({ is_active: false })
        .eq('id', accountId)
        .eq('user_id', user.id); // Ensure user can only remove their own accounts

      if (error) {
        throw error;
      }

      setAccounts(accounts.filter(account => account.id !== accountId));
      toast({
        title: "Account removed",
        description: "The account has been successfully unlinked.",
      });
    } catch (error) {
      console.error('Error removing account:', error);
      toast({
        title: "Error removing account",
        description: "Failed to remove the account. Please try again.",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    console.log('LinkedAccounts useEffect triggered:', {
      authLoading,
      user: !!user,
      userId: user?.id,
      refreshKey
    });

    // Only fetch accounts when auth is done loading and we have a user
    if (!authLoading && user) {
      console.log('LinkedAccounts: Calling fetchLinkedAccounts');
      fetchLinkedAccounts();
    } else if (!authLoading && !user) {
      // Auth is done loading but no user - set loading to false
      console.log('LinkedAccounts: No user, setting loading to false');
      setLoading(false);
    }
  }, [user, authLoading, refreshKey]);

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading accounts...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Linked Accounts</CardTitle>
            <CardDescription>
              Manage your connected debit cards, credit cards, and bank accounts
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={loading}
              onClick={() => {
                setLoading(true);
                fetchLinkedAccounts();
              }}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <p>No accounts linked yet. Link an account to start rounding up your purchases.</p>
            {user && (
              <div className="mt-4 p-3 bg-blue-50 rounded text-sm text-blue-800">
                <p><strong>Troubleshooting:</strong></p>
                <p>• Make sure you're logged in with the correct account</p>
                <p>• Try clicking the "Refresh" button above</p>
                <p>• If you recently linked an account, it may take a moment to appear</p>
                <p className="mt-2 text-xs">Current User: {user.email}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {accounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">
                      {account.account_name || `${account.account_type} Account`}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ••••{account.mask} • {account.account_subtype}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Active
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAccount(account.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
