
import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Trash2, RefreshCw } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useLocation } from 'react-router-dom';

interface LinkedAccount {
  id: string;
  account_name: string;
  account_type: string;
  account_subtype: string;
  mask: string;
  is_active: boolean;
  created_at: string;
}

interface LinkedAccountsProps {
  refreshKey?: number;
}

// Create a global variable to track if we've loaded accounts
let accountsLoaded = false;

export const LinkedAccounts = ({ refreshKey }: LinkedAccountsProps) => {
  const [accounts, setAccounts] = useState<LinkedAccount[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, loading: authLoading } = useAuth();
  const { toast } = useToast();
  const location = useLocation();

  // Debug: Log accounts state changes
  console.log('LinkedAccounts render - accounts state:', accounts, 'length:', accounts.length);
  console.log('LinkedAccounts render - loading:', loading, 'authLoading:', authLoading);
  console.log('LinkedAccounts render - accountsLoaded global:', accountsLoaded);

  const fetchLinkedAccounts = useCallback(async () => {
    if (!user) return;
    
    console.log('LinkedAccounts: Fetching accounts for user:', user.id);
    
    try {
      // Add timestamp to avoid caching issues
      console.log('LinkedAccounts: Making query at:', new Date().toISOString());
      console.log('LinkedAccounts: Querying for user_id:', user.id);

      const { data, error } = await supabase
        .from('linked_accounts')
        .select('id, account_name, account_type, account_subtype, mask, is_active, created_at')
        .eq('user_id', user.id)
        .eq('is_active', true)
        .order('created_at', { ascending: false });
      
      console.log('LinkedAccounts: Raw data from query:', data);
      console.log('LinkedAccounts: Query error:', error);
      console.log('LinkedAccounts: Data type:', typeof data, 'Is array:', Array.isArray(data));
      console.log('LinkedAccounts: Data length:', data?.length);

      if (error) {
        console.error('LinkedAccounts: Error fetching accounts:', error);
        throw error;
      }
      
      if (data && data.length > 0) {
        console.log('LinkedAccounts: Accounts fetched:', data.length);
        setAccounts(data);
        accountsLoaded = true;
        // Store in localStorage as a backup
        localStorage.setItem('linkedAccounts', JSON.stringify(data));
      } else {
        console.log('LinkedAccounts: No accounts found in database');
        // Check if we have accounts in localStorage
        const cachedAccounts = localStorage.getItem('linkedAccounts');
        if (cachedAccounts) {
          console.log('LinkedAccounts: Using cached accounts from localStorage');
          setAccounts(JSON.parse(cachedAccounts));
          accountsLoaded = true;
        } else {
          console.log('LinkedAccounts: No accounts in localStorage either');
          setAccounts([]);
        }
      }
    } catch (error) {
      console.error('LinkedAccounts: Error in fetchLinkedAccounts:', error);
      // Try to load from localStorage as fallback
      const cachedAccounts = localStorage.getItem('linkedAccounts');
      if (cachedAccounts) {
        console.log('LinkedAccounts: Using cached accounts after error');
        setAccounts(JSON.parse(cachedAccounts));
      }
    } finally {
      setLoading(false);
    }
  }, [user]);

  const handleRemoveAccount = async (accountId: string) => {
    if (!user) {
      toast({
        title: "Error",
        description: "User not authenticated.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from('linked_accounts')
        .update({ is_active: false })
        .eq('id', accountId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      // Update local state
      const updatedAccounts = accounts.filter(account => account.id !== accountId);
      setAccounts(updatedAccounts);
      
      // Update localStorage
      localStorage.setItem('linkedAccounts', JSON.stringify(updatedAccounts));
      
      toast({
        title: "Account removed",
        description: "The account has been successfully unlinked.",
      });
    } catch (error) {
      console.error('Error removing account:', error);
      toast({
        title: "Error removing account",
        description: "Failed to remove the account. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Load data on initial mount and when user changes
  useEffect(() => {
    console.log('LinkedAccounts: Main useEffect triggered', { 
      authLoading, 
      user: !!user, 
      accountsLoaded,
      refreshKey
    });

    // First try to load from localStorage to show something immediately
    const cachedAccounts = localStorage.getItem('linkedAccounts');
    if (cachedAccounts && !accountsLoaded) {
      console.log('LinkedAccounts: Loading cached accounts on mount');
      setAccounts(JSON.parse(cachedAccounts));
    }

    // Then fetch fresh data when auth is ready
    if (!authLoading && user) {
      console.log('LinkedAccounts: Auth ready, fetching accounts');
      setLoading(true);
      fetchLinkedAccounts();
    } else if (!authLoading && !user) {
      console.log('LinkedAccounts: Auth ready but no user');
      setLoading(false);
      setAccounts([]);
    }
  }, [user, authLoading, refreshKey, fetchLinkedAccounts]);

  // Force refresh when location changes
  useEffect(() => {
    console.log('LinkedAccounts: Location changed', location.pathname);
    if (user && accountsLoaded) {
      console.log('LinkedAccounts: Refreshing accounts after navigation');
      fetchLinkedAccounts();
    }
  }, [location.pathname, user, fetchLinkedAccounts]);

  if (loading && !accounts.length) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Linked Accounts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4">Loading accounts...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Linked Accounts</CardTitle>
            <CardDescription>
              Manage your connected debit cards, credit cards, and bank accounts
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={loading}
              onClick={() => {
                setLoading(true);
                accountsLoaded = false; // Reset the loaded flag
                fetchLinkedAccounts();
              }}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {accounts.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            <p>No accounts linked yet. Link an account to start rounding up your purchases.</p>
            {user && (
              <div className="mt-4 p-3 bg-blue-50 rounded text-sm text-blue-800">
                <p><strong>Troubleshooting:</strong></p>
                <p>• Make sure you're logged in with the correct account</p>
                <p>• Try clicking the "Refresh" button above</p>
                <p>• If you recently linked an account, it may take a moment to appear</p>
                <p className="mt-2 text-xs">Current User: {user.email}</p>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-3">
            {accounts.map((account) => (
              <div
                key={account.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <div className="font-medium">
                      {account.account_name || `${account.account_type} Account`}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ••••{account.mask} • {account.account_subtype}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Active
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveAccount(account.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
