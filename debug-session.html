<!DOCTYPE html>
<html>
<head>
    <title>Debug Session User ID</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>Debug Session User ID</h1>
    <div id="status"></div>
    <button onclick="checkCurrentSession()">Check Current Session</button>
    <button onclick="signInAndCheck()">Sign In and Check</button>
    
    <script>
        const supabaseUrl = 'https://ltmmbkckohkusutbsbsu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx0bW1ia2Nrb2hrdXN1dGJzYnN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkzNjQzOTYsImV4cCI6MjA2NDk0MDM5Nn0._L2_jT-3A0pCNWuZbcSvNsx_pOp9644MrlI4pTqYpCA';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        function log(message) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML += '<p>' + new Date().toLocaleTimeString() + ': ' + message + '</p>';
            console.log(message);
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        async function checkCurrentSession() {
            try {
                log('🔍 Checking current session...');
                const { data: { session }, error } = await supabase.auth.getSession();
                
                if (error) {
                    log('❌ Session error: ' + error.message);
                    return;
                }
                
                if (session) {
                    log('✅ Session found!');
                    log('📧 Email: ' + session.user.email);
                    log('🆔 Current User ID: ' + session.user.id);
                    log('🎯 Expected User ID: a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4');
                    log('✅ Match: ' + (session.user.id === 'a33f5c9d-d3ee-4646-acc8-c71ec53e9fc4' ? 'YES' : 'NO'));
                    
                    // Check if this user exists in the database
                    await checkUserInDatabase(session.user.id);
                } else {
                    log('❌ No active session');
                }
            } catch (error) {
                log('❌ Exception: ' + error.message);
            }
        }
        
        async function checkUserInDatabase(userId) {
            try {
                log('🔍 Checking if user exists in database...');
                const { data, error } = await supabase
                    .from('users')
                    .select('id, email')
                    .eq('id', userId)
                    .single();
                
                if (error) {
                    log('❌ Database check error: ' + error.message);
                } else if (data) {
                    log('✅ User found in database: ' + data.email);
                } else {
                    log('❌ User NOT found in database');
                }
            } catch (error) {
                log('❌ Database check exception: ' + error.message);
            }
        }
        
        async function signInAndCheck() {
            try {
                log('🔐 Signing in...');
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: '<EMAIL>',
                    password: 'password123'
                });
                
                if (error) {
                    log('❌ Sign in error: ' + error.message);
                } else {
                    log('✅ Signed in successfully!');
                    log('📧 Email: ' + data.user.email);
                    log('🆔 User ID: ' + data.user.id);
                    
                    // Wait a moment for session to update
                    setTimeout(checkCurrentSession, 1000);
                }
            } catch (error) {
                log('❌ Sign in exception: ' + error.message);
            }
        }
        
        // Check session on page load
        window.onload = function() {
            checkCurrentSession();
        };
    </script>
</body>
</html>
